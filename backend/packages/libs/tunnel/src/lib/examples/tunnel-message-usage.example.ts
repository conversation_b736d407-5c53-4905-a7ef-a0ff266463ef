import { Injectable, Inject, Logger } from '@nestjs/common';
import { TunnelMessageService } from '../services/tunnel-message.service';
import { DeviceStatusService } from '@saito/device-status';
import { ModelReportingService } from '@saito/model-reporting';

/**
 * Tunnel消息使用示例
 * 
 * 展示如何使用新的tunnel消息类型来处理设备注册、模型上报和心跳上报
 */
@Injectable()
export class TunnelMessageUsageExample {
  private readonly logger = new Logger(TunnelMessageUsageExample.name);

  constructor(
    private readonly tunnelMessageService: TunnelMessageService,
    @Inject(DeviceStatusService) private readonly deviceStatusService: DeviceStatusService,
    @Inject(ModelReportingService) private readonly modelReportingService: ModelReportingService
  ) {}

  /**
   * 示例：通过tunnel发送设备注册请求
   */
  async sendDeviceRegistrationViaTunnel(targetPeerId: string): Promise<void> {
    try {
      // 获取当前设备信息
      const deviceId = await this.deviceStatusService.getDeviceId();
      const deviceConfig = await this.deviceStatusService.getDeviceConfig();
      
      if (!deviceConfig) {
        throw new Error('Device config not found');
      }

      // 构造设备注册payload
      const payload = {
        code: deviceConfig.code,
        gateway_address: deviceConfig.gatewayAddress,
        reward_address: deviceConfig.rewardAddress,
        device_type: 'GPU', // 可以从系统信息获取
        gpu_type: 'NVIDIA RTX 4090', // 可以从系统信息获取
        ip: '*************', // 可以从系统信息获取
        device_name: deviceConfig.deviceName,
        local_models: [
          {
            name: 'llama3.2:latest',
            size: 2048000000,
            digest: 'sha256:abc123...'
          }
        ]
      };

      // 发送设备注册消息
      await this.tunnelMessageService.sendDeviceRegisterMessage(
        deviceId,
        targetPeerId,
        payload
      );

      this.logger.log('Device registration message sent via tunnel');

    } catch (error) {
      this.logger.error('Failed to send device registration via tunnel:', error);
    }
  }

  /**
   * 示例：通过tunnel发送模型上报
   */
  async sendModelReportViaTunnel(targetPeerId: string): Promise<void> {
    try {
      const deviceId = await this.deviceStatusService.getDeviceId();
      const reportedModels = this.modelReportingService.getReportedModels();

      // 构造模型上报payload
      const payload = {
        device_id: deviceId,
        models: reportedModels.map(modelName => ({
          name: modelName,
          type: 'LLM',
          framework: 'ollama',
          size: 2048000000, // 可以从实际模型信息获取
          capabilities: ['text-generation', 'chat']
        }))
      };

      // 发送模型上报消息
      await this.tunnelMessageService.sendModelReportMessage(
        deviceId,
        targetPeerId,
        payload
      );

      this.logger.log('Model report message sent via tunnel');

    } catch (error) {
      this.logger.error('Failed to send model report via tunnel:', error);
    }
  }

  /**
   * 示例：通过tunnel发送心跳上报
   */
  async sendHeartbeatReportViaTunnel(targetPeerId: string): Promise<void> {
    try {
      const deviceId = await this.deviceStatusService.getDeviceId();
      const deviceConfig = await this.deviceStatusService.getDeviceConfig();
      
      if (!deviceConfig) {
        throw new Error('Device config not found');
      }

      // 构造心跳上报payload（模拟系统信息）
      const payload = {
        code: deviceConfig.code,
        cpu_usage: 45.5,
        memory_usage: 60.2,
        gpu_usage: 80.1,
        gpu_temperature: 75.0,
        network_in_kbps: 1024,
        network_out_kbps: 512,
        ip: '*************',
        timestamp: new Date().toISOString(),
        type: 'GPU',
        model: 'NVIDIA RTX 4090',
        device_info: {
          os: 'Ubuntu 22.04',
          cpu: 'Intel i9-12900K',
          memory: '32GB',
          disk_total: 1000000000000,
          disk_used: 500000000000
        }
      };

      // 发送心跳上报消息
      await this.tunnelMessageService.sendHeartbeatReportMessage(
        deviceId,
        targetPeerId,
        payload
      );

      this.logger.log('Heartbeat report message sent via tunnel');

    } catch (error) {
      this.logger.error('Failed to send heartbeat report via tunnel:', error);
    }
  }

  /**
   * 示例：批量发送消息到多个目标
   */
  async broadcastMessagesToMultipleTargets(targetPeerIds: string[]): Promise<void> {
    try {
      const deviceId = await this.deviceStatusService.getDeviceId();

      // 并行发送到多个目标
      await Promise.all([
        this.tunnelMessageService.broadcastDeviceRegisterMessage(deviceId, targetPeerIds, {
          code: 'device-code-123',
          gateway_address: 'https://gateway.example.com',
          reward_address: '0x123...',
          device_type: 'GPU',
          gpu_type: 'NVIDIA RTX 4090',
          ip: '*************'
        }),
        
        this.tunnelMessageService.broadcastModelReportMessage(deviceId, targetPeerIds, {
          device_id: deviceId,
          models: [{ name: 'llama3.2:latest', type: 'LLM' }]
        }),
        
        this.tunnelMessageService.broadcastHeartbeatReportMessage(deviceId, targetPeerIds, {
          code: 'device-code-123',
          cpu_usage: 45.5,
          memory_usage: 60.2,
          gpu_usage: 80.1,
          gpu_temperature: 75.0,
          network_in_kbps: 1024,
          network_out_kbps: 512,
          ip: '*************',
          timestamp: new Date().toISOString(),
          type: 'GPU',
          model: 'NVIDIA RTX 4090',
          device_info: {}
        })
      ]);

      this.logger.log(`Messages broadcasted to ${targetPeerIds.length} targets`);

    } catch (error) {
      this.logger.error('Failed to broadcast messages:', error);
    }
  }
}
