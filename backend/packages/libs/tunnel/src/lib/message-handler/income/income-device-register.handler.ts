import { Injectable, Logger } from '@nestjs/common';
import { IncomeBaseMessageHandler } from '../base-message-handler';
import { TunnelMessage, DeviceRegisterMessage, DeviceRegisterMessageSchema } from '@saito/models';
import { MessageHandler } from '../message-handler.decorator';

/**
 * 设备注册请求消息入站处理器
 *
 * 处理通过tunnel接收到的设备注册请求，记录日志并可以触发事件
 * 注意：为避免循环依赖，此处理器不直接调用DeviceStatusService
 */
@MessageHandler({ type: 'device_register', direction: 'income' })
@Injectable()
export class IncomeDeviceRegisterHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeDeviceRegisterHandler.name);

  constructor() {
    super();
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    try {
      const registerMessage = DeviceRegisterMessageSchema.parse(message) as DeviceRegisterMessage;
      
      this.logger.log(`Processing device register request from ${registerMessage.from}`);
      this.logger.debug(`Register payload:`, registerMessage.payload);

      // 记录设备注册请求信息
      this.logger.log(`Device register request details:`, {
        code: registerMessage.payload.code,
        gateway_address: registerMessage.payload.gateway_address,
        reward_address: registerMessage.payload.reward_address,
        device_type: registerMessage.payload.device_type,
        gpu_type: registerMessage.payload.gpu_type,
        ip: registerMessage.payload.ip,
        device_name: registerMessage.payload.device_name,
        local_models_count: registerMessage.payload.local_models?.length || 0
      });

      // TODO: 这里可以发送事件或者通过其他方式通知设备注册服务
      // 避免直接依赖DeviceStatusService以防止循环依赖

      // 这里可以发送响应消息回给发送方
      // 或者触发其他后续处理逻辑

    } catch (error) {
      this.logger.error('Error processing device register message:', error);
      
      // 这里可以发送错误响应消息
    }
  }
}
