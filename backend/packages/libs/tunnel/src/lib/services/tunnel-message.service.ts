import { Injectable, Logger } from '@nestjs/common';
import {
  DeviceRegisterMessage,
  ModelReportMessage,
  HeartbeatReportMessage,
  DeviceRegisterPayload,
  ModelReportPayload,
  HeartbeatReportPayload
} from '@saito/models';
import { MessageHandlerRegistry } from '../message-handler/message-handler.registry';

/**
 * Tunnel消息发送服务
 * 
 * 提供便捷的方法来发送各种类型的tunnel消息
 */
@Injectable()
export class TunnelMessageService {
  private readonly logger = new Logger(TunnelMessageService.name);

  constructor(
    private readonly messageHandlerRegistry: MessageHandlerRegistry
  ) {}

  /**
   * 发送设备注册请求消息
   */
  async sendDeviceRegisterMessage(
    from: string,
    to: string,
    payload: DeviceRegisterPayload
  ): Promise<void> {
    const message: DeviceRegisterMessage = {
      type: 'device_register',
      from,
      to,
      timestamp: Date.now(),
      payload
    };

    this.logger.log(`Sending device register message from ${from} to ${to}`);
    
    // 通过outcome处理器发送消息
    await this.messageHandlerRegistry.handleOutcomeMessage(message);
  }

  /**
   * 发送模型上报消息
   */
  async sendModelReportMessage(
    from: string,
    to: string,
    payload: ModelReportPayload
  ): Promise<void> {
    const message: ModelReportMessage = {
      type: 'model_report',
      from,
      to,
      timestamp: Date.now(),
      payload
    };

    this.logger.log(`Sending model report message from ${from} to ${to}`);
    
    // 通过outcome处理器发送消息
    await this.messageHandlerRegistry.handleOutcomeMessage(message);
  }

  /**
   * 发送心跳上报消息
   */
  async sendHeartbeatReportMessage(
    from: string,
    to: string,
    payload: HeartbeatReportPayload
  ): Promise<void> {
    const message: HeartbeatReportMessage = {
      type: 'heartbeat_report',
      from,
      to,
      timestamp: Date.now(),
      payload
    };

    this.logger.log(`Sending heartbeat report message from ${from} to ${to}`);
    
    // 通过outcome处理器发送消息
    await this.messageHandlerRegistry.handleOutcomeMessage(message);
  }

  /**
   * 批量发送设备注册请求（支持多个目标）
   */
  async broadcastDeviceRegisterMessage(
    from: string,
    targets: string[],
    payload: DeviceRegisterPayload
  ): Promise<void> {
    const promises = targets.map(to => 
      this.sendDeviceRegisterMessage(from, to, payload)
    );
    
    await Promise.all(promises);
    this.logger.log(`Broadcasted device register message to ${targets.length} targets`);
  }

  /**
   * 批量发送模型上报消息（支持多个目标）
   */
  async broadcastModelReportMessage(
    from: string,
    targets: string[],
    payload: ModelReportPayload
  ): Promise<void> {
    const promises = targets.map(to => 
      this.sendModelReportMessage(from, to, payload)
    );
    
    await Promise.all(promises);
    this.logger.log(`Broadcasted model report message to ${targets.length} targets`);
  }

  /**
   * 批量发送心跳上报消息（支持多个目标）
   */
  async broadcastHeartbeatReportMessage(
    from: string,
    targets: string[],
    payload: HeartbeatReportPayload
  ): Promise<void> {
    const promises = targets.map(to => 
      this.sendHeartbeatReportMessage(from, to, payload)
    );
    
    await Promise.all(promises);
    this.logger.log(`Broadcasted heartbeat report message to ${targets.length} targets`);
  }
}
