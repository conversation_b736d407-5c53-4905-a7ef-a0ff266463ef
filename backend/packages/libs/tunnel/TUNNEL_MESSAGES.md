# Tunnel消息类型扩展

本文档描述了为tunnel模块新增的三种消息类型，用于处理设备注册、模型上报和心跳上报。

## 新增消息类型

### 1. 设备注册消息 (`device_register`)

用于通过tunnel发送设备注册请求。

**消息结构：**
```typescript
interface DeviceRegisterMessage {
  type: 'device_register';
  from: string;
  to: string;
  timestamp?: number;
  payload: {
    code: string;                    // 设备代码
    gateway_address: string;         // 网关地址
    reward_address: string;          // 奖励地址
    device_type: string;             // 设备类型
    gpu_type: string;                // GPU类型
    ip: string;                      // IP地址
    device_name?: string;            // 设备名称
    local_models?: Array<{           // 本地模型列表
      name: string;
      size: number;
      digest: string;
    }>;
  };
}
```

### 2. 模型上报消息 (`model_report`)

用于通过tunnel发送模型上报请求。

**消息结构：**
```typescript
interface ModelReportMessage {
  type: 'model_report';
  from: string;
  to: string;
  timestamp?: number;
  payload: {
    device_id: string;               // 设备ID
    models: Array<Record<string, any>>; // 模型数据列表
  };
}
```

### 3. 心跳上报消息 (`heartbeat_report`)

用于通过tunnel发送心跳上报请求。

**消息结构：**
```typescript
interface HeartbeatReportMessage {
  type: 'heartbeat_report';
  from: string;
  to: string;
  timestamp?: number;
  payload: {
    code: string;                    // 设备代码
    cpu_usage: number;               // CPU使用率 (0-100)
    memory_usage: number;            // 内存使用率 (0-100)
    gpu_usage: number;               // GPU使用率 (0-100)
    gpu_temperature: number;         // GPU温度
    network_in_kbps: number;         // 入站网络流量 (kbps)
    network_out_kbps: number;        // 出站网络流量 (kbps)
    ip: string;                      // 设备IP地址
    timestamp: string;               // 时间戳
    type: string;                    // 设备类型
    model: string;                   // 设备型号
    device_info: Record<string, any>; // 设备详细信息
  };
}
```

## 使用方法

### 1. 注入TunnelMessageService

```typescript
import { Injectable, Inject } from '@nestjs/common';
import { TunnelMessageService } from '@saito/tunnel';

@Injectable()
export class YourService {
  constructor(
    private readonly tunnelMessageService: TunnelMessageService
  ) {}
}
```

### 2. 发送设备注册消息

```typescript
async sendDeviceRegistration() {
  await this.tunnelMessageService.sendDeviceRegisterMessage(
    'your-device-id',
    'target-peer-id',
    {
      code: 'device-code-123',
      gateway_address: 'https://gateway.example.com',
      reward_address: '0x123...',
      device_type: 'GPU',
      gpu_type: 'NVIDIA RTX 4090',
      ip: '*************',
      device_name: 'My Device',
      local_models: [
        {
          name: 'llama3.2:latest',
          size: 2048000000,
          digest: 'sha256:abc123...'
        }
      ]
    }
  );
}
```

### 3. 发送模型上报消息

```typescript
async sendModelReport() {
  await this.tunnelMessageService.sendModelReportMessage(
    'your-device-id',
    'target-peer-id',
    {
      device_id: 'your-device-id',
      models: [
        {
          name: 'llama3.2:latest',
          type: 'LLM',
          framework: 'ollama',
          size: 2048000000,
          capabilities: ['text-generation', 'chat']
        }
      ]
    }
  );
}
```

### 4. 发送心跳上报消息

```typescript
async sendHeartbeatReport() {
  await this.tunnelMessageService.sendHeartbeatReportMessage(
    'your-device-id',
    'target-peer-id',
    {
      code: 'device-code-123',
      cpu_usage: 45.5,
      memory_usage: 60.2,
      gpu_usage: 80.1,
      gpu_temperature: 75.0,
      network_in_kbps: 1024,
      network_out_kbps: 512,
      ip: '*************',
      timestamp: new Date().toISOString(),
      type: 'GPU',
      model: 'NVIDIA RTX 4090',
      device_info: {
        os: 'Ubuntu 22.04',
        cpu: 'Intel i9-12900K',
        memory: '32GB'
      }
    }
  );
}
```

### 5. 批量发送消息

```typescript
async broadcastMessages() {
  const targetPeerIds = ['peer1', 'peer2', 'peer3'];
  
  // 批量发送设备注册消息
  await this.tunnelMessageService.broadcastDeviceRegisterMessage(
    'your-device-id',
    targetPeerIds,
    payload
  );
  
  // 批量发送模型上报消息
  await this.tunnelMessageService.broadcastModelReportMessage(
    'your-device-id',
    targetPeerIds,
    payload
  );
  
  // 批量发送心跳上报消息
  await this.tunnelMessageService.broadcastHeartbeatReportMessage(
    'your-device-id',
    targetPeerIds,
    payload
  );
}
```

## 消息处理器

每种消息类型都有对应的入站和出站处理器：

### 入站处理器 (Income Handlers)
- `IncomeDeviceRegisterHandler` - 处理接收到的设备注册请求
- `IncomeModelReportHandler` - 处理接收到的模型上报请求
- `IncomeHeartbeatReportHandler` - 处理接收到的心跳上报请求

### 出站处理器 (Outcome Handlers)
- `OutcomeDeviceRegisterHandler` - 处理发送的设备注册请求
- `OutcomeModelReportHandler` - 处理发送的模型上报请求
- `OutcomeHeartbeatReportHandler` - 处理发送的心跳上报请求

## 数据格式兼容性

这些新的消息类型的数据格式与现有的API格式保持兼容：

- **设备注册**: 兼容 `/node/register` API的请求格式
- **模型上报**: 兼容 `/node/report-models` API的请求格式
- **心跳上报**: 兼容 `/node/heartbeat/new` API的请求格式

这样可以无缝地在API调用和tunnel消息之间进行切换。

## 注意事项

1. 所有消息都会经过相应的处理器进行验证和处理
2. 消息发送失败会抛出异常，需要适当的错误处理
3. 批量发送时，单个消息失败不会影响其他消息的发送
4. 建议在生产环境中添加适当的重试机制和错误恢复逻辑
