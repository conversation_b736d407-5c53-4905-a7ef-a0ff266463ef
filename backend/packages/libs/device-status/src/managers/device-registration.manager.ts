import { Injectable, Logger, Inject } from '@nestjs/common';
import { ModelOfMiner } from '@saito/models';
import { SystemInfoCollector } from '../collectors/system-info.collector';
import { DeviceConfigManager } from './device-config.manager';
import { ErrorHandler } from '../utils/error-handler';
import { TunnelCommunicationService } from '../services/tunnel-communication.service';
import { DidServiceInterface } from '@saito/did';

/**
 * 扩展的注册响应接口
 */
export interface ExtendedRegistrationResponse extends ModelOfMiner<'RegistrationResponse'> {
  config?: ModelOfMiner<'DeviceConfig'>;
}

/**
 * 设备注册管理器
 * 
 * 负责：
 * 1. 设备注册流程
 * 2. 自动重连逻辑
 * 3. 注册状态管理
 */
@Injectable()
export class DeviceRegistrationManager {
  private readonly logger = new Logger(DeviceRegistrationManager.name);
  private readonly errorHandler = new ErrorHandler(DeviceRegistrationManager.name);

  constructor(
    private readonly systemInfoCollector: SystemInfoCollector,
    private readonly configManager: DeviceConfigManager,
    @Inject(TunnelCommunicationService) private readonly tunnelService: TunnelCommunicationService,
    @Inject('DidService') private readonly didService: DidServiceInterface
  ) {}

  /**
   * 注册设备 - 仅使用WebSocket协议，使用DID中的设备ID
   */
  async registerDevice(
    credentials: ModelOfMiner<'DeviceCredentials'>,
    localModels: any,
    targetPeerId: string = 'gateway',
    isAutoReconnect: boolean = false
  ): Promise<ExtendedRegistrationResponse> {
    return this.errorHandler.safeExecute(
      async () => {
        // 从DID服务获取设备ID
        let didDeviceId: string;
        try {
          didDeviceId = this.didService.getMyPeerId();
          this.logger.log(`📱 使用DID中的设备ID: ${didDeviceId}`);
        } catch (error) {
          this.logger.error('❌ 无法从DID服务获取设备ID:', error);
          throw new Error('Failed to get device ID from DID service');
        }

        const [ipAddress, deviceType, deviceModel] = await Promise.all([
          this.systemInfoCollector.getIpAddress(),
          this.systemInfoCollector.getDeviceType(),
          this.systemInfoCollector.getDeviceModel()
        ]);

        // 构造注册数据，包含DID的设备ID
        const registrationData = {
          code: credentials.code,
          gateway_address: credentials.gateway_address,
          reward_address: credentials.reward_address,
          device_type: deviceType,
          gpu_type: deviceModel,
          ip: ipAddress,
          device_id: didDeviceId, // 添加DID的设备ID
          device_name: `Device-${didDeviceId.slice(-8)}`, // 使用DID ID的后8位作为设备名
          local_models: localModels?.map((model: any) => ({
            name: model.name || '',
            size: model.size || 0,
            digest: model.digest || ''
          })) || []
        };

        // 通过WebSocket发送注册请求
        const tunnelResult = await this.tunnelService.sendDeviceRegistration(
          didDeviceId,
          targetPeerId,
          registrationData
        );

        if (tunnelResult) {
          // 创建配置，使用DID的设备ID
          const newConfig: ModelOfMiner<'DeviceConfig'> = {
            deviceId: didDeviceId, // 使用DID的设备ID
            deviceName: registrationData.device_name,
            rewardAddress: credentials.reward_address,
            gatewayAddress: credentials.gateway_address,
            key: credentials.key,
            code: credentials.code,
            isRegistered: true
          };

          // 保存配置（只在非自动重连时保存）
          if (!isAutoReconnect) {
            await this.configManager.saveConfigToStorage(newConfig, credentials.basePath);
          }

          this.logger.log(`✅ 设备注册成功 via WebSocket: ${didDeviceId}`);

          return {
            success: true,
            error: '',
            node_id: didDeviceId,
            name: newConfig.deviceName,
            config: newConfig
          };
        }

        return {
          success: false,
          error: 'Failed to send registration via WebSocket',
          node_id: undefined,
          name: undefined
        };
      },
      'device-registration',
      {
        success: false,
        error: 'Registration failed due to system error',
        node_id: undefined,
        name: undefined
      }
    );
  }

  /**
   * 通过WebSocket注册设备 - 使用DID中的设备ID（已废弃，使用registerDevice方法）
   * @deprecated 使用 registerDevice 方法替代
   */
  async registerDeviceViaTunnel(
    credentials: ModelOfMiner<'DeviceCredentials'>,
    localModels: any,
    targetPeerId: string,
    _fromPeerId: string, // 不再使用，改为从DID服务获取
    isAutoReconnect: boolean = false
  ): Promise<ExtendedRegistrationResponse> {
    this.logger.warn('⚠️ registerDeviceViaTunnel is deprecated, use registerDevice instead');

    // 直接调用新的registerDevice方法
    return this.registerDevice(credentials, localModels, targetPeerId, isAutoReconnect);
  }

  /**
   * 检查是否可以自动重连
   */
  canAutoReconnect(): boolean {
    const config = this.configManager.getCurrentConfig();
    return config.isRegistered && !!config.gatewayAddress && !!config.key;
  }

  /**
   * 创建重连凭据
   */
  createReconnectCredentials(): ModelOfMiner<'DeviceCredentials'> | null {
    if (!this.canAutoReconnect()) {
      return null;
    }

    const config = this.configManager.getCurrentConfig();
    return {
      gateway_address: config.gatewayAddress,
      reward_address: config.rewardAddress,
      key: config.key,
      code: config.code,
      basePath: this.configManager.getBasePath()
    };
  }

  /**
   * 清除注册信息
   */
  async clearRegistration(): Promise<boolean> {
    return this.errorHandler.safeExecute(
      async () => {
        return await this.configManager.resetConfig();
      },
      'clear-registration',
      false
    );
  }

  /**
   * 获取注册状态
   */
  getRegistrationStatus(): {
    isRegistered: boolean;
    deviceId: string;
    deviceName: string;
    gatewayAddress: string;
  } {
    const config = this.configManager.getCurrentConfig();
    return {
      isRegistered: config.isRegistered,
      deviceId: config.deviceId,
      deviceName: config.deviceName,
      gatewayAddress: config.gatewayAddress
    };
  }
}
