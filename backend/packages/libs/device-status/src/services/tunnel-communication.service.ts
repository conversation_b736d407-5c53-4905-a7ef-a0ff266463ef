import { Injectable, Logger, Inject } from '@nestjs/common';
import { TunnelMessageService } from '@saito/tunnel';
import {
  DeviceRegisterRequestPayload,
  DeviceModelReportPayload,
  DeviceHeartbeatReportPayload
} from '@saito/models';

/**
 * Tunnel通信服务
 *
 * 负责通过tunnel发送设备注册、模型上报和心跳上报消息
 */
@Injectable()
export class TunnelCommunicationService {
  private readonly logger = new Logger(TunnelCommunicationService.name);

  constructor(
    @Inject('TunnelService') private readonly tunnelService: any,
    private readonly tunnelMessageService: TunnelMessageService
  ) {}

  /**
   * 通过tunnel发送设备注册请求
   */
  async sendDeviceRegistration(
    fromPeerId: string,
    toPeerId: string,
    registrationData: {
      code: string;
      gateway_address: string;
      reward_address: string;
      device_type?: string;
      gpu_type?: string;
      ip?: string;
      basePath?: string;
    }
  ): Promise<boolean> {
    try {
      this.logger.log(`Sending device registration from ${fromPeerId} to ${toPeerId}`);

      // 首先建立tunnel连接
      if (registrationData.gateway_address && registrationData.code) {
        await this.tunnelService.createSocket(
          registrationData.gateway_address,
          'device-key', // 临时使用，实际应该从配置获取
          registrationData.code,
          registrationData.basePath || '/'
        );

        // 等待连接建立并监听响应
        await this.waitForRegistrationResponse(fromPeerId);
      }

      // 构造设备注册payload
      const payload: DeviceRegisterRequestPayload = {
        code: registrationData.code,
        gateway_address: registrationData.gateway_address,
        reward_address: registrationData.reward_address,
        device_type: registrationData.device_type,
        gpu_type: registrationData.gpu_type,
        ip: registrationData.ip
      };

      // 发送设备注册消息
      await this.tunnelMessageService.sendDeviceRegisterMessage(
        fromPeerId,
        toPeerId,
        payload
      );

      this.logger.log(`Device registration sent successfully via tunnel`);
      return true;

    } catch (error) {
      this.logger.error('Failed to send device registration via tunnel:', error);
      return false;
    }
  }

  /**
   * 通过tunnel发送模型上报请求
   */
  async sendModelReport(
    fromPeerId: string,
    toPeerId: string,
    reportData: {
      device_id: string;
      models: Array<{
        name: string;
        modified_at: string;
        size: number;
        digest: string;
        details: {
          format: string;
          family: string;
          families: string[];
          parameter_size: string;
          quantization_level: string;
        };
      }>;
    }
  ): Promise<boolean> {
    try {
      this.logger.log(`Sending model report from ${fromPeerId} to ${toPeerId}`);

      // 构造模型上报payload
      const payload: DeviceModelReportPayload = {
        device_id: reportData.device_id,
        models: reportData.models
      };

      // 发送模型上报消息
      await this.tunnelMessageService.sendModelReportMessage(
        fromPeerId,
        toPeerId,
        payload
      );

      // 等待模型上报响应
      const responseReceived = await this.waitForModelReportResponse(fromPeerId);

      if (responseReceived) {
        this.logger.log(`Model report sent successfully via tunnel`);
        return true;
      } else {
        this.logger.warn(`Model report sent but no response received`);
        return false;
      }

    } catch (error) {
      this.logger.error('Failed to send model report via tunnel:', error);
      return false;
    }
  }

  /**
   * 通过tunnel发送心跳上报请求
   */
  async sendHeartbeatReport(
    fromPeerId: string,
    toPeerId: string,
    heartbeatData: {
      code: string;
      cpu_usage?: number;
      memory_usage?: number;
      gpu_usage?: number;
      ip?: string;
      timestamp?: string;
      type?: string;
      model?: string;
      device_info?: {
        cpu_model?: string;
        cpu_cores?: number;
        cpu_threads?: number;
        ram_total?: number;
        gpu_model?: string;
        gpu_count?: number;
        gpu_memory?: number;
        disk_total?: number;
        os_info?: string;
      };
    }
  ): Promise<boolean> {
    try {
      this.logger.log(`Sending heartbeat report from ${fromPeerId} to ${toPeerId}`);

      // 构造心跳上报payload
      const payload: DeviceHeartbeatReportPayload = {
        code: heartbeatData.code,
        cpu_usage: heartbeatData.cpu_usage,
        memory_usage: heartbeatData.memory_usage,
        gpu_usage: heartbeatData.gpu_usage,
        ip: heartbeatData.ip,
        timestamp: heartbeatData.timestamp,
        type: heartbeatData.type,
        model: heartbeatData.model,
        device_info: heartbeatData.device_info
      };

      // 发送心跳上报消息
      await this.tunnelMessageService.sendHeartbeatReportMessage(
        fromPeerId,
        toPeerId,
        payload
      );

      // 等待心跳上报响应
      const responseReceived = await this.waitForHeartbeatResponse(fromPeerId);

      if (responseReceived) {
        this.logger.log(`Heartbeat report sent successfully via tunnel`);
        return true;
      } else {
        this.logger.warn(`Heartbeat report sent but no response received`);
        return false;
      }

    } catch (error) {
      this.logger.error('Failed to send heartbeat report via tunnel:', error);
      return false;
    }
  }

  /**
   * 等待设备注册响应
   * 简化版本：由于连接建立本身就表示注册成功，我们直接等待一小段时间确保连接稳定
   */
  private async waitForRegistrationResponse(_deviceId: string): Promise<boolean> {
    // 等待2秒确保连接稳定
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 检查连接状态
    if (this.tunnelService.isConnected && this.tunnelService.isConnected()) {
      this.logger.log('Device registration successful - connection established');
      return true;
    } else {
      this.logger.warn('Device registration may have failed - no stable connection');
      return false;
    }
  }

  /**
   * 等待模型上报响应
   * 简化版本：假设消息发送成功就表示上报成功
   */
  private async waitForModelReportResponse(_deviceId: string): Promise<boolean> {
    // 等待1秒确保消息发送完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    this.logger.log('Model report assumed successful');
    return true;
  }

  /**
   * 等待心跳上报响应
   * 简化版本：假设消息发送成功就表示心跳成功
   */
  private async waitForHeartbeatResponse(_deviceId: string): Promise<boolean> {
    // 等待1秒确保消息发送完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    this.logger.log('Heartbeat report assumed successful');
    return true;
  }

}

export const TunnelCommunicationServiceProvider = {
  provide: TunnelCommunicationService,
  useClass: TunnelCommunicationService,
};

export default TunnelCommunicationServiceProvider;
