import { Injectable, Logger } from '@nestjs/common';

/**
 * Tunnel通信服务
 *
 * 负责通过tunnel发送设备注册、模型上报和心跳上报消息
 * 注意：为避免循环依赖，此服务暂时只提供接口，实际实现需要在应用层完成
 */
@Injectable()
export class TunnelCommunicationService {
  private readonly logger = new Logger(TunnelCommunicationService.name);

  constructor() {}

  /**
   * 通过tunnel发送设备注册请求
   */
  async sendDeviceRegistration(
    fromPeerId: string,
    toPeerId: string,
    registrationData: any
  ): Promise<boolean> {
    this.logger.log(`Would send device register message from ${fromPeerId} to ${toPeerId}`);
    // TODO: 实际实现tunnel消息发送
    return true;
  }

  /**
   * 通过tunnel发送模型上报请求
   */
  async sendModelReport(
    fromPeerId: string,
    toPeerId: string,
    reportData: any
  ): Promise<boolean> {
    this.logger.log(`Would send model report message from ${fromPeerId} to ${toPeerId}`);
    // TODO: 实际实现tunnel消息发送
    return true;
  }

  /**
   * 通过tunnel发送心跳上报请求
   */
  async sendHeartbeatReport(
    fromPeerId: string,
    toPeerId: string,
    heartbeatData: any
  ): Promise<boolean> {
    this.logger.log(`Would send heartbeat report message from ${fromPeerId} to ${toPeerId}`);
    // TODO: 实际实现tunnel消息发送
    return true;
  }


}

export const TunnelCommunicationServiceProvider = {
  provide: TunnelCommunicationService,
  useClass: TunnelCommunicationService,
};

export default TunnelCommunicationServiceProvider;
