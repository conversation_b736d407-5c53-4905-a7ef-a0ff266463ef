import { Injectable, Logger } from '@nestjs/common';
import { TunnelMessageService } from '@saito/tunnel';
import {
  DeviceRegisterRequestPayload,
  DeviceModelReportPayload,
  DeviceHeartbeatReportPayload
} from '@saito/models';

/**
 * Tunnel通信服务
 * 
 * 负责通过tunnel发送设备注册、模型上报和心跳上报消息
 */
@Injectable()
export class TunnelCommunicationService {
  private readonly logger = new Logger(TunnelCommunicationService.name);

  constructor(
    private readonly tunnelMessageService: TunnelMessageService
  ) {}

  /**
   * 通过tunnel发送设备注册请求
   */
  async sendDeviceRegistration(
    fromPeerId: string,
    toPeerId: string,
    registrationData: {
      code: string;
      gateway_address: string;
      reward_address: string;
      device_type: string;
      gpu_type: string;
      ip: string;
      device_name?: string;
      local_models?: Array<{
        name: string;
        size: number;
        digest: string;
      }>;
    }
  ): Promise<boolean> {
    try {
      const payload: DeviceRegisterRequestPayload = {
        code: registrationData.code,
        gateway_address: registrationData.gateway_address,
        reward_address: registrationData.reward_address,
        device_type: registrationData.device_type,
        gpu_type: registrationData.gpu_type,
        ip: registrationData.ip,
        local_models: registrationData.local_models
      };

      await this.tunnelMessageService.sendDeviceRegisterMessage(
        fromPeerId,
        toPeerId,
        payload
      );

      this.logger.log(`Device registration sent via tunnel from ${fromPeerId} to ${toPeerId}`);
      return true;

    } catch (error) {
      this.logger.error('Failed to send device registration via tunnel:', error);
      return false;
    }
  }

  /**
   * 通过tunnel发送模型上报请求
   */
  async sendModelReport(
    fromPeerId: string,
    toPeerId: string,
    reportData: {
      device_id: string;
      models: Array<Record<string, any>>;
    }
  ): Promise<boolean> {
    try {
      const payload: DeviceModelReportPayload = {
        device_id: reportData.device_id,
        models: reportData.models
      };

      await this.tunnelMessageService.sendModelReportMessage(
        fromPeerId,
        toPeerId,
        payload
      );

      this.logger.log(`Model report sent via tunnel from ${fromPeerId} to ${toPeerId}`);
      return true;

    } catch (error) {
      this.logger.error('Failed to send model report via tunnel:', error);
      return false;
    }
  }

  /**
   * 通过tunnel发送心跳上报请求
   */
  async sendHeartbeatReport(
    fromPeerId: string,
    toPeerId: string,
    heartbeatData: {
      code: string;
      cpu_usage: number;
      memory_usage: number;
      gpu_usage: number;
      gpu_temperature: number;
      network_in_kbps: number;
      network_out_kbps: number;
      ip: string;
      timestamp: string;
      type: string;
      model: string;
      device_info: Record<string, any>;
    }
  ): Promise<boolean> {
    try {
      const payload: DeviceHeartbeatReportPayload = {
        code: heartbeatData.code,
        cpu_usage: heartbeatData.cpu_usage,
        memory_usage: heartbeatData.memory_usage,
        gpu_usage: heartbeatData.gpu_usage,
        ip: heartbeatData.ip,
        timestamp: heartbeatData.timestamp,
        type: heartbeatData.type,
        model: heartbeatData.model,
        device_info: heartbeatData.device_info
      };

      await this.tunnelMessageService.sendHeartbeatReportMessage(
        fromPeerId,
        toPeerId,
        payload
      );

      this.logger.log(`Heartbeat report sent via tunnel from ${fromPeerId} to ${toPeerId}`);
      return true;

    } catch (error) {
      this.logger.error('Failed to send heartbeat report via tunnel:', error);
      return false;
    }
  }

  /**
   * 批量发送设备注册到多个目标
   */
  async broadcastDeviceRegistration(
    fromPeerId: string,
    targetPeerIds: string[],
    registrationData: any
  ): Promise<boolean> {
    try {
      const promises = targetPeerIds.map(toPeerId => 
        this.sendDeviceRegistration(fromPeerId, toPeerId, registrationData)
      );
      
      const results = await Promise.all(promises);
      const successCount = results.filter(result => result).length;
      
      this.logger.log(`Device registration broadcast: ${successCount}/${targetPeerIds.length} successful`);
      return successCount > 0;

    } catch (error) {
      this.logger.error('Failed to broadcast device registration:', error);
      return false;
    }
  }

  /**
   * 批量发送模型上报到多个目标
   */
  async broadcastModelReport(
    fromPeerId: string,
    targetPeerIds: string[],
    reportData: any
  ): Promise<boolean> {
    try {
      const promises = targetPeerIds.map(toPeerId => 
        this.sendModelReport(fromPeerId, toPeerId, reportData)
      );
      
      const results = await Promise.all(promises);
      const successCount = results.filter(result => result).length;
      
      this.logger.log(`Model report broadcast: ${successCount}/${targetPeerIds.length} successful`);
      return successCount > 0;

    } catch (error) {
      this.logger.error('Failed to broadcast model report:', error);
      return false;
    }
  }

  /**
   * 批量发送心跳上报到多个目标
   */
  async broadcastHeartbeatReport(
    fromPeerId: string,
    targetPeerIds: string[],
    heartbeatData: any
  ): Promise<boolean> {
    try {
      const promises = targetPeerIds.map(toPeerId => 
        this.sendHeartbeatReport(fromPeerId, toPeerId, heartbeatData)
      );
      
      const results = await Promise.all(promises);
      const successCount = results.filter(result => result).length;
      
      this.logger.log(`Heartbeat report broadcast: ${successCount}/${targetPeerIds.length} successful`);
      return successCount > 0;

    } catch (error) {
      this.logger.error('Failed to broadcast heartbeat report:', error);
      return false;
    }
  }
}

export const TunnelCommunicationServiceProvider = {
  provide: TunnelCommunicationService,
  useClass: TunnelCommunicationService,
};

export default TunnelCommunicationServiceProvider;
