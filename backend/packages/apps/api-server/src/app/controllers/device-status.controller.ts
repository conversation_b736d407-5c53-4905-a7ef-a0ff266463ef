import { Body, Controller, Get, Inject, Logger, Post, Res, Query } from "@nestjs/common";
import { DeviceStatusService, TunnelCommunicationService } from "@saito/device-status";
import { Response } from 'express';
import { createZodDto } from "nestjs-zod";
import { DeviceCredentials } from "@saito/models";


export class RegisterDeviceDto extends createZodDto(DeviceCredentials) {}



@Controller('/api/v1/device-status')
export class DeviceStatusController {
  private readonly logger = new Logger(DeviceStatusController.name);
  constructor(
    @Inject(DeviceStatusService) private readonly deviceStatusService: DeviceStatusService,
    @Inject(TunnelCommunicationService) private readonly tunnelService: TunnelCommunicationService
  ) {}

  @Post('/register')
  async register(@Res() res: Response, @Body() body: RegisterDeviceDto) {
    try {
      // 首先尝试通过tunnel发送注册请求
      try {
        const deviceId = await this.deviceStatusService.getDeviceId();
        const tunnelResult = await this.tunnelService.sendDeviceRegistration(
          deviceId,
          'gateway', // 固定发送给网关
          {
            code: body.code,
            gateway_address: body.gateway_address,
            reward_address: body.reward_address,
            device_type: 'GPU',
            gpu_type: '',
            ip: '*************',
            basePath: body.basePath
          }
        );

        if (tunnelResult) {
          this.logger.log('Device registration sent via tunnel');
        }
      } catch (tunnelError) {
        this.logger.warn('Failed to send registration via tunnel:', tunnelError);
      }

      // 继续执行原有的HTTP注册逻辑
      const data = await this.deviceStatusService.register(body);

      if (data.success) {
        res.status(200).json({
          success: true,
          message: 'Registration successful, starting heartbeat',
          deviceId: data.node_id,
          deviceName: data.name,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(400).json({
          success: false,
          error: data.error || 'Registration failed',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.logger.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Post('/heartbeat')
  async heartbeat(@Res() res: Response) {
    try {
      // 首先尝试通过tunnel发送心跳
      try {
        const deviceId = await this.deviceStatusService.getDeviceId();
        const heartbeatData = {
          code: 'device-code-123', // 临时硬编码，实际应该从设备配置获取
          cpu_usage: 45.5,
          memory_usage: 60.2,
          gpu_usage: 80.1,
          ip: '*************',
          timestamp: new Date().toISOString(),
          type: 'GPU',
          model: 'NVIDIA RTX 4090',
          device_info: {
            cpu_model: 'Intel i9-12900K',
            cpu_cores: 12,
            cpu_threads: 20,
            ram_total: 32,
            gpu_model: 'NVIDIA RTX 4090',
            gpu_count: 1,
            gpu_memory: 24,
            disk_total: 1000,
            os_info: 'Ubuntu 22.04'
          }
        };

        const tunnelResult = await this.tunnelService.sendHeartbeatReport(
          deviceId,
          'gateway', // 固定发送给网关
          heartbeatData
        );

        if (tunnelResult) {
          this.logger.log('Heartbeat sent via tunnel');
        }
      } catch (tunnelError) {
        this.logger.warn('Failed to send heartbeat via tunnel:', tunnelError);
      }

      // 返回成功响应
      res.status(200).json({
        success: true,
        message: 'Heartbeat sent successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Heartbeat error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }




  @Get('/gateway-status')
  async getGatewayStatus() {
    return this.deviceStatusService.getGatewayStatus();
  }

  @Get('/gateway-address')
  async getGatewayAddress() {
    const gatewayAddress = await this.deviceStatusService.getGatewayAddress();
    return { gatewayAddress };
  }


}
