import { Body, Controller, Get, Inject, Logger, Post, Res, Query } from "@nestjs/common";
import { DeviceStatusService, TunnelCommunicationService } from "@saito/device-status";
import { Response } from 'express';
import { createZodDto } from "nestjs-zod";
import { DeviceCredentials } from "@saito/models";
import { z } from 'zod';
import * as R from 'ramda';

export class RegisterDeviceDto extends createZodDto(DeviceCredentials) {}

// Tunnel相关的DTO
const TunnelRegisterSchema = z.object({
  fromPeerId: z.string(),
  toPeerId: z.string(),
  credentials: DeviceCredentials
});

const TunnelModelReportSchema = z.object({
  fromPeerId: z.string(),
  toPeerId: z.string(),
  models: z.array(z.string())
});

const TunnelHeartbeatSchema = z.object({
  fromPeerId: z.string(),
  toPeerId: z.string()
});

export class TunnelRegisterDto extends createZodDto(TunnelRegisterSchema) {}
export class TunnelModelReportDto extends createZodDto(TunnelModelReportSchema) {}
export class TunnelHeartbeatDto extends createZodDto(TunnelHeartbeatSchema) {}

@Controller('/api/v1/device-status')
export class DeviceStatusController {
  private readonly logger = new Logger(DeviceStatusController.name);
  constructor(
    @Inject(DeviceStatusService) private readonly deviceStatusService: DeviceStatusService,
    @Inject(TunnelCommunicationService) private readonly tunnelService: TunnelCommunicationService
  ) {}

  @Post('/register')
  async register(@Res() res: Response, @Body() body: RegisterDeviceDto) {
    try {
      const data = await this.deviceStatusService.register(body);

      if (data.success) {
        res.status(200).json({
          success: true,
          message: 'Registration successful, starting heartbeat',
          deviceId: data.node_id,
          deviceName: data.name,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(400).json({
          success: false,
          error: data.error || 'Registration failed',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.logger.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Post('/register-via-tunnel')
  async registerViaTunnel(@Res() res: Response, @Body() body: TunnelRegisterDto) {
    try {
      const result = await this.tunnelService.sendDeviceRegistration(
        body.fromPeerId,
        body.toPeerId,
        {
          code: body.credentials.code,
          gateway_address: body.credentials.gateway_address,
          reward_address: body.credentials.reward_address,
          device_type: 'GPU',
          gpu_type: 'NVIDIA RTX 4090',
          ip: '*************',
          device_name: `Device-${Date.now()}`
        }
      );

      if (result) {
        res.status(200).json({
          success: true,
          message: 'Registration request sent via tunnel',
          fromPeerId: body.fromPeerId,
          toPeerId: body.toPeerId,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Failed to send registration via tunnel',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.logger.error('Tunnel registration error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Post('/heartbeat-via-tunnel')
  async heartbeatViaTunnel(@Res() res: Response, @Body() body: TunnelHeartbeatDto) {
    try {
      const deviceConfig = await this.deviceStatusService.getDeviceInfo();

      if (!deviceConfig) {
        res.status(400).json({
          success: false,
          error: 'Device not configured',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const heartbeatData = {
        code: 'device-code-123', // 临时硬编码，实际应该从设备配置获取
        cpu_usage: 45.5,
        memory_usage: 60.2,
        gpu_usage: 80.1,
        ip: '*************',
        timestamp: new Date().toISOString(),
        type: 'GPU',
        model: 'NVIDIA RTX 4090',
        device_info: {
          cpu_model: 'Intel i9-12900K',
          cpu_cores: 12,
          cpu_threads: 20,
          ram_total: 32,
          gpu_model: 'NVIDIA RTX 4090',
          gpu_count: 1,
          gpu_memory: 24,
          disk_total: 1000,
          os_info: 'Ubuntu 22.04'
        }
      };

      const result = await this.tunnelService.sendHeartbeatReport(
        body.fromPeerId,
        body.toPeerId,
        heartbeatData
      );

      if (result) {
        res.status(200).json({
          success: true,
          message: 'Heartbeat sent via tunnel',
          fromPeerId: body.fromPeerId,
          toPeerId: body.toPeerId,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Failed to send heartbeat via tunnel',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.logger.error('Tunnel heartbeat error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Get('/gateway-status')
  async getGatewayStatus() {
    return this.deviceStatusService.getGatewayStatus();
  }

  @Get('/gateway-address')
  async getGatewayAddress() {
    const gatewayAddress = await this.deviceStatusService.getGatewayAddress();
    return { gatewayAddress };
  }


}
