import { Body, Controller, Get, Inject, Logger, Post, Res, Query } from "@nestjs/common";
import { DeviceStatusService, TunnelCommunicationService, SystemInfoCollector } from "@saito/device-status";
import { Response } from 'express';
import { createZodDto } from "nestjs-zod";
import { DeviceCredentials } from "@saito/models";


export class RegisterDeviceDto extends createZodDto(DeviceCredentials) {}



@Controller('/api/v1/device-status')
export class DeviceStatusController {
  private readonly logger = new Logger(DeviceStatusController.name);
  constructor(
    @Inject(DeviceStatusService) private readonly deviceStatusService: DeviceStatusService,
    @Inject(TunnelCommunicationService) private readonly tunnelService: TunnelCommunicationService,
    private readonly systemInfoCollector: SystemInfoCollector
  ) {}

  @Post('/register')
  async register(@Res() res: Response, @Body() body: RegisterDeviceDto) {
    try {
      // 首先尝试通过tunnel发送注册请求，使用自动获取的数据
      try {
        const deviceId = await this.deviceStatusService.getDeviceId();

        // 自动获取系统信息
        const [ipAddress, deviceType, deviceModel] = await Promise.all([
          this.systemInfoCollector.getIpAddress(),
          this.systemInfoCollector.getDeviceType(),
          this.systemInfoCollector.getDeviceModel()
        ]);

        const tunnelResult = await this.tunnelService.sendDeviceRegistration(
          deviceId,
          'gateway', // 固定发送给网关
          {
            code: body.code,
            gateway_address: body.gateway_address,
            reward_address: body.reward_address,
            device_type: deviceType, // 自动获取设备类型
            gpu_type: deviceModel, // 自动获取GPU型号
            ip: ipAddress, // 自动获取IP地址
            device_id: deviceId, // 添加DID设备ID
            device_name: `Device-${deviceId.slice(-8)}`, // 基于DID ID生成设备名
            basePath: body.basePath,
            local_models: [] // 可以后续添加模型列表获取
          }
        );

        if (tunnelResult) {
          this.logger.log(`✅ 设备注册成功 via WebSocket: ${deviceId}`);
        }
      } catch (tunnelError) {
        this.logger.warn('❌ Failed to send registration via tunnel:', tunnelError);
      }

      // 继续执行原有的HTTP注册逻辑
      const data = await this.deviceStatusService.register(body);

      if (data.success) {
        res.status(200).json({
          success: true,
          message: 'Registration successful, starting heartbeat',
          deviceId: data.node_id,
          deviceName: data.name,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(400).json({
          success: false,
          error: data.error || 'Registration failed',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      this.logger.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  @Post('/heartbeat')
  async heartbeat(@Res() res: Response) {
    try {
      // 首先尝试通过tunnel发送心跳，使用自动获取的数据
      try {
        const deviceId = await this.deviceStatusService.getDeviceId();

        // 自动获取系统信息
        const [ipAddress, deviceType, deviceModel, systemInfo] = await Promise.all([
          this.systemInfoCollector.getIpAddress(),
          this.systemInfoCollector.getDeviceType(),
          this.systemInfoCollector.getDeviceModel(),
          this.systemInfoCollector.getSystemInfo()
        ]);

        // 获取设备配置中的认证码
        const config = await this.deviceStatusService.getCurrentConfig();

        const heartbeatData = {
          code: config.code || deviceId, // 使用配置中的认证码或设备ID
          cpu_usage: systemInfo.cpu?.usage || 0,
          memory_usage: systemInfo.memory?.usage || 0,
          gpu_usage: systemInfo.gpu?.usage || 0,
          ip: ipAddress,
          timestamp: new Date().toISOString(),
          type: deviceType,
          model: deviceModel,
          device_info: {
            cpu_model: systemInfo.cpu?.model || 'Unknown',
            cpu_cores: systemInfo.cpu?.cores || 0,
            cpu_threads: systemInfo.cpu?.threads || 0,
            ram_total: systemInfo.memory?.total || 0,
            gpu_model: deviceModel,
            gpu_count: systemInfo.gpu?.count || 0,
            gpu_memory: systemInfo.gpu?.memory || 0,
            disk_total: systemInfo.disk?.total || 0,
            os_info: systemInfo.os?.platform || 'Unknown'
          }
        };

        const tunnelResult = await this.tunnelService.sendHeartbeatReport(
          deviceId,
          'gateway', // 固定发送给网关
          heartbeatData
        );

        if (tunnelResult) {
          this.logger.log(`💓 心跳发送成功 via WebSocket: ${deviceId}`);
        }
      } catch (tunnelError) {
        this.logger.warn('❌ Failed to send heartbeat via tunnel:', tunnelError);
      }

      // 返回成功响应
      res.status(200).json({
        success: true,
        message: 'Heartbeat sent successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Heartbeat error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }




  @Get('/gateway-status')
  async getGatewayStatus() {
    return this.deviceStatusService.getGatewayStatus();
  }

  @Get('/gateway-address')
  async getGatewayAddress() {
    const gatewayAddress = await this.deviceStatusService.getGatewayAddress();
    return { gatewayAddress };
  }


}
