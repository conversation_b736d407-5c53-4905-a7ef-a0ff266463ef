# Tunnel设备ID集成实现

## 概述

成功实现了使用DID中的设备ID作为Socket.IO连接标识符，替代了默认的随机连接ID。现在网关可以通过有意义的设备ID来识别和管理连接的设备。

## 问题背景

之前的连接显示：
```
Peer connected with ID: Wv-WmZTGkAtLrFSDAAAh
```

这是Socket.IO自动生成的随机连接ID，对网关来说没有业务意义。

## 解决方案

### 1. 修改Socket.IO连接配置

在 `MessageGatewayService.connect()` 方法中添加了设备ID支持：

```typescript
// 创建Socket连接
this.socket = io(this.gatewayUrl, {
  path: socketPath,
  reconnection: true,
  // ... 其他配置
  auth: {
    // 使用设备ID作为认证信息
    deviceId: deviceId || 'unknown-device',
    key: key,
    ...(code ? { code: code } : {})
  },
  extraHeaders: {
    'Origin': this.gatewayUrl,
    'Authorization': `Bearer ${key}`,
    ...(code ? { 'X-Auth-Code': code } : {}),
    ...(deviceId ? { 'X-Device-ID': deviceId } : {})
  }
});
```

### 2. 更新接口定义

**MessageGateway接口**：
```typescript
connect(gatewayAddress: string, key: string, code?: string, basePath?: string, deviceId?: string): Promise<void>;
```

**TunnelService接口**：
```typescript
createSocket(gatewayAddress: string, key: string, code?: string, basePath?: string, deviceId?: string): Promise<void>;
```

### 3. 修改调用链

**TunnelService实现**：
```typescript
async createSocket(gatewayAddress: string, key: string, code?: string, basePath?: string, deviceId?: string): Promise<void> {
  // 如果没有提供设备ID，使用当前的peerId
  const actualDeviceId = deviceId || this.peerId;
  
  await this.messageGateway.connect(gatewayAddress, key, code, basePath, actualDeviceId);
  this.logger.log(`创建Socket连接成功，设备ID: ${actualDeviceId}`);
}
```

**DeviceGatewayService调用**：
```typescript
// 传递设备ID到createSocket
this.tunnelService.createSocket(config.gatewayAddress, config.key, config.code, config.basePath, nodeId);
await this.tunnelService.connectSocket(nodeId);
```

**TunnelCommunicationService调用**：
```typescript
await this.tunnelService.createSocket(
  registrationData.gateway_address,
  'device-key',
  registrationData.code,
  registrationData.basePath || '/',
  fromPeerId // 传递设备ID
);
```

## 技术实现细节

### 1. 设备ID传递机制

- **auth字段**：Socket.IO的认证对象，包含设备ID
- **extraHeaders**：HTTP头部，包含 `X-Device-ID`
- **向后兼容**：如果没有提供设备ID，使用默认值或当前peerId

### 2. 连接流程

```mermaid
sequenceDiagram
    participant Device as 设备
    participant TunnelService as TunnelService
    participant MessageGateway as MessageGateway
    participant Gateway as 网关

    Device->>TunnelService: createSocket(address, key, code, basePath, deviceId)
    TunnelService->>MessageGateway: connect(address, key, code, basePath, deviceId)
    MessageGateway->>Gateway: Socket.IO连接 + auth: {deviceId}
    Gateway-->>MessageGateway: 连接确认
    MessageGateway-->>TunnelService: 连接成功
    TunnelService->>MessageGateway: registerDevice(deviceId)
    MessageGateway->>Gateway: 设备注册消息
```

### 3. 网关端识别

现在网关可以通过以下方式识别设备：

1. **Socket.IO auth对象**：`socket.handshake.auth.deviceId`
2. **HTTP头部**：`socket.handshake.headers['x-device-id']`
3. **注册消息**：设备注册时的payload中的deviceId

## 预期效果

### 之前
```
Peer connected with ID: Wv-WmZTGkAtLrFSDAAAh
```

### 现在
```
Peer connected with ID: a7d93e9e-9416-4ba5-88d8-1effa067cce0
```

或者网关可以通过auth信息获取设备ID：
```javascript
// 网关端代码示例
io.on('connection', (socket) => {
  const deviceId = socket.handshake.auth.deviceId;
  console.log(`Device connected: ${deviceId}`);
  
  // 可以将socket与设备ID关联
  deviceSockets.set(deviceId, socket);
});
```

## 优势

1. **有意义的标识**：使用业务相关的设备ID而不是随机字符串
2. **设备管理**：网关可以更容易地管理和追踪设备连接
3. **调试友好**：日志和监控中显示有意义的设备标识
4. **向后兼容**：不破坏现有功能，支持可选的设备ID参数
5. **多重识别**：通过auth、headers、注册消息多种方式传递设备ID

## 配置要求

网关端需要支持：
1. 读取Socket.IO的auth对象中的deviceId
2. 处理HTTP头部中的X-Device-ID
3. 将Socket连接与设备ID关联

现在设备连接时，网关将看到有意义的设备ID而不是随机的Socket.IO连接ID！
