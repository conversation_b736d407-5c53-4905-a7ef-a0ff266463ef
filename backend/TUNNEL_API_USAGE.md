# Tunnel API 使用指南

本文档介绍如何使用新的tunnel模块来实现设备注册、模型上报和心跳上报功能。

## 概述

我们已经成功修改了现有的设备注册、模型上报和心跳上报接口，使其支持通过tunnel模块进行通信。这提供了一种新的通信方式，可以与现有的HTTP API并行使用。

## 新增的API端点

### 1. 通过Tunnel注册设备

**端点**: `POST /api/v1/device-status/register-via-tunnel`

**请求体**:
```json
{
  "fromPeerId": "your-device-peer-id",
  "toPeerId": "target-peer-id",
  "credentials": {
    "code": "device-code-123",
    "gateway_address": "https://gateway.example.com",
    "reward_address": "0x123...",
    "key": "device-key"
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "Registration request sent via tunnel",
  "fromPeerId": "your-device-peer-id",
  "toPeerId": "target-peer-id",
  "timestamp": "2025-06-23T10:30:00.000Z"
}
```

### 2. 通过Tunnel上报模型

**端点**: `POST /api/v1/device-status/report-models-via-tunnel`

**请求体**:
```json
{
  "fromPeerId": "your-device-peer-id",
  "toPeerId": "target-peer-id",
  "models": ["llama3.2:latest", "mistral:7b"]
}
```

**响应**:
```json
{
  "success": true,
  "message": "Model report sent via tunnel",
  "fromPeerId": "your-device-peer-id",
  "toPeerId": "target-peer-id",
  "models": ["llama3.2:latest", "mistral:7b"],
  "timestamp": "2025-06-23T10:30:00.000Z"
}
```

### 3. 通过Tunnel发送心跳

**端点**: `POST /api/v1/device-status/heartbeat-via-tunnel`

**请求体**:
```json
{
  "fromPeerId": "your-device-peer-id",
  "toPeerId": "target-peer-id"
}
```

**响应**:
```json
{
  "success": true,
  "message": "Heartbeat sent via tunnel",
  "fromPeerId": "your-device-peer-id",
  "toPeerId": "target-peer-id",
  "timestamp": "2025-06-23T10:30:00.000Z"
}
```

## 使用示例

### 使用curl命令

```bash
# 1. 通过tunnel注册设备
curl -X POST http://localhost:3000/api/v1/device-status/register-via-tunnel \
  -H "Content-Type: application/json" \
  -d '{
    "fromPeerId": "device-001",
    "toPeerId": "gateway-001",
    "credentials": {
      "code": "ABC123",
      "gateway_address": "https://gateway.example.com",
      "reward_address": "0x1234567890abcdef",
      "key": "secret-key"
    }
  }'

# 2. 通过tunnel上报模型
curl -X POST http://localhost:3000/api/v1/device-status/report-models-via-tunnel \
  -H "Content-Type: application/json" \
  -d '{
    "fromPeerId": "device-001",
    "toPeerId": "gateway-001",
    "models": ["llama3.2:latest", "mistral:7b"]
  }'

# 3. 通过tunnel发送心跳
curl -X POST http://localhost:3000/api/v1/device-status/heartbeat-via-tunnel \
  -H "Content-Type: application/json" \
  -d '{
    "fromPeerId": "device-001",
    "toPeerId": "gateway-001"
  }'
```

### 使用JavaScript/TypeScript

```typescript
import axios from 'axios';

const API_BASE = 'http://localhost:3000/api/v1/device-status';

// 1. 通过tunnel注册设备
async function registerViaTunnel() {
  try {
    const response = await axios.post(`${API_BASE}/register-via-tunnel`, {
      fromPeerId: 'device-001',
      toPeerId: 'gateway-001',
      credentials: {
        code: 'ABC123',
        gateway_address: 'https://gateway.example.com',
        reward_address: '0x1234567890abcdef',
        key: 'secret-key'
      }
    });
    
    console.log('Registration result:', response.data);
  } catch (error) {
    console.error('Registration failed:', error.response?.data || error.message);
  }
}

// 2. 通过tunnel上报模型
async function reportModelsViaTunnel() {
  try {
    const response = await axios.post(`${API_BASE}/report-models-via-tunnel`, {
      fromPeerId: 'device-001',
      toPeerId: 'gateway-001',
      models: ['llama3.2:latest', 'mistral:7b']
    });
    
    console.log('Model report result:', response.data);
  } catch (error) {
    console.error('Model report failed:', error.response?.data || error.message);
  }
}

// 3. 通过tunnel发送心跳
async function heartbeatViaTunnel() {
  try {
    const response = await axios.post(`${API_BASE}/heartbeat-via-tunnel`, {
      fromPeerId: 'device-001',
      toPeerId: 'gateway-001'
    });
    
    console.log('Heartbeat result:', response.data);
  } catch (error) {
    console.error('Heartbeat failed:', error.response?.data || error.message);
  }
}
```

## 架构说明

### 新增组件

1. **TunnelCommunicationService**: 负责通过tunnel发送各种类型的消息
2. **新的消息类型**: 
   - `device_register`: 设备注册消息
   - `model_report`: 模型上报消息
   - `heartbeat_report`: 心跳上报消息
3. **消息处理器**: 每种消息类型都有对应的入站和出站处理器

### 数据流

1. **HTTP API** → **TunnelCommunicationService** → **TunnelMessageService** → **消息处理器** → **Tunnel网络**
2. **Tunnel网络** → **消息处理器** → **业务逻辑处理** → **日志记录**

### 兼容性

- 新的tunnel API与现有的HTTP API完全兼容
- 可以同时使用两种方式进行通信
- 现有的业务逻辑保持不变

## 优势

1. **去中心化通信**: 不依赖中心化的HTTP服务器
2. **P2P网络**: 直接在设备之间进行通信
3. **容错性**: 提供多种通信方式的冗余
4. **扩展性**: 易于添加新的消息类型和处理逻辑

## 注意事项

1. 确保tunnel模块已正确配置和启动
2. 设备的peer ID需要正确设置
3. 网络连接需要支持P2P通信
4. 消息的可靠性依赖于tunnel网络的稳定性

## 故障排除

1. **消息发送失败**: 检查peer ID是否正确，网络连接是否正常
2. **消息未收到**: 检查消息处理器是否正确注册
3. **循环依赖错误**: 确保模块导入顺序正确

## 下一步

1. 添加消息确认机制
2. 实现消息重试逻辑
3. 添加消息加密和签名
4. 实现消息路由和转发功能
